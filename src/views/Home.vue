<template>
  <div class="home-container">
    <!-- 分类容器 -->
    <div class="category-section">
      <!-- 标题 -->
      <div class="section-header">
        <h2 class="section-title">Explore Services</h2>
      </div>
      
      <!-- 分类网格容器 -->
      <div class="category-grid-container" :class="{ 'expanded': isExpanded }">
        <div class="category-grid">
          <!-- 前7个分类 -->
          <div
            v-for="(category, index) in visibleCategories"
            :key="category.id"
            class="category-item"
            @click="handleCategoryClick(category)"
          >
            <div class="category-icon">
              <van-icon :name="category.icon" :color="category.color" size="24" />
            </div>
            <span class="category-name">{{ category.name }}</span>
          </div>

          <!-- 展开/收起按钮 -->
          <div
            class="category-item expand-item"
            @click="toggleExpand"
          >
            <div class="category-icon expand-icon" :class="{ 'rotating': isAnimating }">
              <van-icon
                :name="isExpanded ? 'arrow-up' : 'arrow-down'"
                color="#7ed321"
                size="20"
              />
            </div>
            <span class="category-name">{{ isExpanded ? 'Less' : 'More' }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索栏容器 -->
    <div class="search-section">
      <div class="search-container">
        <van-search
          v-model="searchQuery"
          placeholder="Search for products..."
          show-action
          shape="round"
          background="#ffffff"
          @search="onSearch"
          @cancel="onCancel"
          class="custom-search"
        >
          <template #action>
            <div class="search-action" @click="onSearch">Search</div>
          </template>
        </van-search>
      </div>
    </div>

    <!-- 商品列表容器 -->
    <div class="products-section">
      <!-- 列表头部 -->
      <div class="products-header">
        <h3 class="products-title">Featured Products</h3>
        <div class="layout-toggle">
          <van-icon
            name="bars"
            :color="layoutMode === 'list' ? '#7ed321' : '#999999'"
            size="20"
            @click="setLayoutMode('list')"
            class="layout-icon"
          />
          <van-icon
            name="apps-o"
            :color="layoutMode === 'grid' ? '#7ed321' : '#999999'"
            size="20"
            @click="setLayoutMode('grid')"
            class="layout-icon"
          />
        </div>
      </div>

      <!-- 商品列表 -->
      <div class="products-grid" :class="{ 'list-mode': layoutMode === 'list' }">
        <div
          v-for="product in products"
          :key="product.id"
          class="product-item"
          @click="handleProductClick(product)"
        >
          <div class="product-image">
            <img :src="product.image" :alt="product.name" />
            <div class="product-badge" v-if="product.discount">
              -{{ product.discount }}%
            </div>
          </div>
          <div class="product-info">
            <div class="product-content">
              <h4 class="product-name">{{ product.name }}</h4>
              <p class="product-description">{{ product.description }}</p>
            </div>
            <div class="product-footer">
              <div class="product-left">
                <div class="product-price">
                  <span class="current-price">${{ product.price }}</span>
                  <span v-if="product.originalPrice" class="original-price">
                    ${{ product.originalPrice }}
                  </span>
                </div>
                <div class="product-rating">
                  <van-icon name="star" color="#FFD700" size="12" />
                  <span class="rating-text">{{ product.rating }}</span>
                  <span class="rating-count">({{ product.reviews }})</span>
                </div>
              </div>
              <div class="add-to-cart-btn" @click.stop="addToCart(product)">
                <van-icon name="plus" color="#ffffff" size="16" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'

// 响应式数据
const isExpanded = ref(false)
const isAnimating = ref(false)
const searchQuery = ref('')
const layoutMode = ref('grid') // 'grid' 或 'list'

// 分类数据
const categories = ref([
  { id: 1, name: 'Groceries', icon: 'shopping-cart-o', color: '#7ed321' },
  { id: 2, name: 'Fresh Fruits', icon: 'gift-o', color: '#ff6b6b' },
  { id: 3, name: 'Vegetables', icon: 'flower-o', color: '#4ecdc4' },
  { id: 4, name: 'Dairy', icon: 'bag-o', color: '#45b7d1' },
  { id: 5, name: 'Meat & Fish', icon: 'fire-o', color: '#f39c12' },
  { id: 6, name: 'Beverages', icon: 'coffee-o', color: '#9b59b6' },
  { id: 7, name: 'Snacks', icon: 'star-o', color: '#e74c3c' },
  { id: 8, name: 'Bakery', icon: 'gem-o', color: '#f1c40f' },
  { id: 9, name: 'Frozen', icon: 'diamond-o', color: '#3498db' },
  { id: 10, name: 'Health', icon: 'like-o', color: '#2ecc71' },
  { id: 11, name: 'Beauty', icon: 'smile-o', color: '#e91e63' },
  { id: 12, name: 'Household', icon: 'home-o', color: '#795548' }
])

// 商品数据
const products = ref([
  {
    id: 1,
    name: 'Fresh Organic Apples',
    description: 'Sweet and crispy organic apples',
    price: 4.99,
    originalPrice: 6.99,
    discount: 29,
    rating: 4.8,
    reviews: 124,
    image: 'https://images.unsplash.com/photo-**********-1e4cd0b6cbd6?w=300&h=300&fit=crop'
  },
  {
    id: 2,
    name: 'Premium Avocados',
    description: 'Ripe and creamy avocados',
    price: 3.49,
    rating: 4.6,
    reviews: 89,
    image: 'https://images.unsplash.com/photo-1523049673857-eb18f1d7b578?w=300&h=300&fit=crop'
  },
  {
    id: 3,
    name: 'Organic Bananas',
    description: 'Fresh yellow bananas',
    price: 2.99,
    originalPrice: 3.99,
    discount: 25,
    rating: 4.7,
    reviews: 156,
    image: 'https://images.unsplash.com/photo-1571771894821-ce9b6c11b08e?w=300&h=300&fit=crop'
  },
  {
    id: 4,
    name: 'Fresh Strawberries',
    description: 'Sweet and juicy strawberries',
    price: 5.99,
    rating: 4.9,
    reviews: 203,
    image: 'https://images.unsplash.com/photo-1464965911861-746a04b4bca6?w=300&h=300&fit=crop'
  }
])

// 计算显示的分类
const visibleCategories = computed(() => {
  if (isExpanded.value) {
    return categories.value.slice(0, 11) // 显示前11个分类
  } else {
    return categories.value.slice(0, 7) // 显示前7个分类
  }
})

// 切换展开状态
const toggleExpand = () => {
  isAnimating.value = true
  isExpanded.value = !isExpanded.value

  // 动画完成后重置状态
  setTimeout(() => {
    isAnimating.value = false
  }, 300)
}

// 设置布局模式
const setLayoutMode = (mode) => {
  layoutMode.value = mode
}

// 处理分类点击
const handleCategoryClick = (category) => {
  console.log('点击分类:', category.name)
  // 这里可以添加跳转到分类页面的逻辑
}

// 处理商品点击
const handleProductClick = (product) => {
  console.log('点击商品:', product.name)
  // 这里可以添加跳转到商品详情页面的逻辑
}

// 搜索功能
const onSearch = () => {
  console.log('搜索:', searchQuery.value)
  // 这里可以添加搜索逻辑
}

const onCancel = () => {
  searchQuery.value = ''
  console.log('取消搜索')
}

// 添加到购物车
const addToCart = (product) => {
  console.log('添加到购物车:', product.name)
  // 这里可以添加购物车逻辑
  // 可以显示一个提示消息
}
</script>

<style scoped>
/* 首页容器 */
.home-container {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 20px 16px;
}

/* 分类区域 */
.category-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 区域标题 */
.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

/* 分类网格容器 */
.category-grid-container {
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.category-grid-container:not(.expanded) {
  max-height: 176px; /* 2行高度：(48px图标 + 8px底边距 + 16px文字 + 12px行间距) * 2 + 16px容器间距 */
}

.category-grid-container.expanded {
  max-height: 264px; /* 3行高度：(48px图标 + 8px底边距 + 16px文字 + 12px行间距) * 3 + 16px容器间距 */
}

/* 分类网格 */
.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
}

/* 分类项 */
.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background-color: #f8f9fa;
}

.category-item:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
}

.category-item:active {
  transform: translateY(0);
}

/* 分类图标 */
.category-icon {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.category-item:hover .category-icon {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 展开按钮特殊样式 */
.expand-item .category-icon {
  background-color: rgba(126, 211, 33, 0.1);
  border: 2px solid rgba(126, 211, 33, 0.2);
}

.expand-icon {
  background-color: rgba(126, 211, 33, 0.1) !important;
  transition: transform 0.3s ease;
}

.expand-icon.rotating {
  transform: rotate(180deg);
}

/* 分类名称 */
.category-name {
  font-size: 12px;
  color: #666666;
  text-align: center;
  font-weight: 500;
  line-height: 1.2;
}

.expand-item .category-name {
  color: #7ed321;
  font-weight: 600;
}

/* 搜索栏区域 */
.search-section {
  margin: 16px 0;
}

.search-container {
  background: #ffffff;
  border-radius: 12px;
  padding: 8px 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.custom-search {
  --van-search-background: transparent;
  --van-search-content-background: #f8f9fa;
  --van-search-input-height: 44px;
  --van-field-input-text-color: #1a1a1a;
  --van-field-placeholder-text-color: #999999;
}

:deep(.custom-search .van-search__content) {
  border-radius: 22px;
  border: 1px solid #e5e5e5;
  transition: all 0.3s ease;
}

:deep(.custom-search .van-search__content:focus-within) {
  border-color: #7ed321;
  background-color: #ffffff;
  box-shadow: 0 0 0 3px rgba(126, 211, 33, 0.1);
}

.search-action {
  color: #7ed321;
  font-weight: 600;
  font-size: 14px;
  padding: 0 8px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.search-action:hover {
  color: #6bc91a;
}

/* 商品列表区域 */
.products-section {
  background: #ffffff;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.products-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

.layout-toggle {
  display: flex;
  gap: 12px;
}

.layout-icon {
  padding: 8px;
  border-radius: 6px;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.3s ease;
}

.layout-icon:hover {
  background-color: #e9ecef;
}

/* 商品网格 */
.products-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
  transition: all 0.3s ease;
}

.products-grid.list-mode {
  grid-template-columns: 1fr;
}

/* 商品项 */
.product-item {
  background: #f8f9fa;
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.product-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-color: #7ed321;
}

.list-mode .product-item {
  display: flex;
  flex-direction: row;
}

.list-mode .product-item .product-image {
  width: 120px;
  height: 120px;
  flex-shrink: 0;
}

.list-mode .product-item .product-info {
  flex: 1;
  padding: 16px;
}

/* 商品图片 */
.product-image {
  position: relative;
  width: 100%;
  height: 160px;
  overflow: hidden;
}

.product-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.product-item:hover .product-image img {
  transform: scale(1.05);
}

.product-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #e74c3c;
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

/* 商品信息 */
.product-info {
  padding: 12px;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.product-description {
  font-size: 12px;
  color: #666666;
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.product-price-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 8px;
}

.add-to-cart-btn {
  width: 28px;
  height: 28px;
  background: #7ed321;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 6px rgba(126, 211, 33, 0.3);
}

.add-to-cart-btn:hover {
  background: #6bc91a;
  transform: scale(1.1);
  box-shadow: 0 4px 12px rgba(126, 211, 33, 0.4);
}

.add-to-cart-btn:active {
  transform: scale(0.95);
}

.current-price {
  font-size: 16px;
  font-weight: 700;
  color: #7ed321;
}

.original-price {
  font-size: 12px;
  color: #999999;
  text-decoration: line-through;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 4px;
}

.rating-text {
  font-size: 12px;
  font-weight: 600;
  color: #1a1a1a;
}

.rating-count {
  font-size: 11px;
  color: #999999;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .home-container {
    padding: 16px 12px;
  }

  .category-section,
  .search-section .search-container,
  .products-section {
    padding: 16px;
  }

  .section-title,
  .products-title {
    font-size: 16px;
  }

  .category-grid-container {
    max-height: 148px; /* 移动端精确高度：(40px图标 + 20px间距 + 12px文字) * 2行 + 12px间距 */
  }

  .category-grid-container.expanded {
    max-height: 220px; /* 移动端展开精确高度：(40px图标 + 20px间距 + 12px文字) * 3行 + 24px间距 */
  }

  .category-grid {
    gap: 12px;
  }

  .category-item {
    padding: 10px 6px;
  }

  .category-icon {
    width: 40px;
    height: 40px;
  }

  .category-name {
    font-size: 11px;
  }

  .products-grid {
    gap: 12px;
  }

  .product-image {
    height: 140px;
  }

  .list-mode .product-item .product-image {
    width: 100px;
    height: 100px;
  }

  .list-mode .product-item .product-info {
    padding: 12px;
  }

  .layout-toggle {
    gap: 8px;
  }

  .layout-icon {
    padding: 6px;
  }
}
</style>
