<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
    </div>
    
    <!-- 登录表单卡片 -->
    <div class="login-card">
      <!-- Logo区域 -->
      <div class="logo-section">
        <div class="logo-container">
          <div class="logo-icon">
            <svg viewBox="0 0 100 100" class="logo-svg">
              <!-- 外圆环 -->
              <circle cx="50" cy="50" r="45" fill="none" stroke="#7ed321" stroke-width="3" opacity="0.3"/>
              <!-- 内圆 -->
              <circle cx="50" cy="50" r="35" fill="#7ed321" opacity="0.1"/>
              <!-- 购物袋图标 -->
              <path d="M30 35 L70 35 L68 75 L32 75 Z" fill="#7ed321" opacity="0.8"/>
              <path d="M35 35 L35 25 Q35 20 40 20 L60 20 Q65 20 65 25 L65 35"
                    fill="none" stroke="#7ed321" stroke-width="2.5" stroke-linecap="round"/>
              <!-- 装饰点 -->
              <circle cx="42" cy="50" r="2" fill="#7ed321"/>
              <circle cx="58" cy="50" r="2" fill="#7ed321"/>
              <circle cx="50" cy="60" r="1.5" fill="#7ed321" opacity="0.6"/>
            </svg>
          </div>
          <div class="logo-text">
            <h2 class="brand-name">US Shop</h2>
            <p class="brand-tagline">Fresh & Natural</p>
          </div>
        </div>
      </div>

      <!-- 标题区域 -->
      <div class="login-header">
        <h1 class="login-title">Log in to your account</h1>
        <p class="login-subtitle">Welcome back! Please enter your details</p>
      </div>
      
      <!-- 表单区域 -->
      <van-form @submit="onSubmit" class="login-form">
        <div class="form-group">
          <label class="form-label">Account</label>
          <van-field
            v-model="formData.account"
            name="account"
            placeholder="Enter your account"
            :rules="[{ required: true, message: 'Please enter your account' }]"
            class="custom-field"
          />
        </div>
        
        <div class="form-group">
          <label class="form-label">Password</label>
          <van-field
            v-model="formData.password"
            type="password"
            name="password"
            placeholder="Enter your password"
            :rules="[{ required: true, message: 'Please enter your password' }]"
            class="custom-field"
          />
        </div>
        
        <!-- 登录按钮 -->
        <van-button
          round
          block
          type="primary"
          native-type="submit"
          class="login-button"
          :loading="loading"
        >
          Log in
        </van-button>
      </van-form>
      
      <!-- 底部链接 -->
      <div class="login-footer">
        <p class="footer-text">
          Don't have an account? 
          <span class="link-text">Sign up</span>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 路由实例
const router = useRouter()

// 响应式数据
const formData = ref({
  account: '',
  password: ''
})

const loading = ref(false)

// 提交表单
const onSubmit = async (values) => {
  loading.value = true

  try {
    // 模拟登录请求
    console.log('登录数据:', values)

    // 这里可以添加实际的登录逻辑
    await new Promise(resolve => setTimeout(resolve, 1500))

    // 登录成功后跳转到首页
    router.push('/home')

  } catch (error) {
    console.error('登录失败:', error)
    alert('登录失败，请重试')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
/* 登录容器 */
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: linear-gradient(45deg, rgba(126, 211, 33, 0.1), rgba(126, 211, 33, 0.05));
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: -100px;
  right: -100px;
}

.circle-2 {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: -75px;
}

.circle-3 {
  width: 100px;
  height: 100px;
  top: 50%;
  right: 10%;
  transform: translateY(-50%);
}

/* 登录卡片 */
.login-card {
  background: #ffffff;
  border-radius: 16px;
  padding: 40px 24px 32px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  position: relative;
  z-index: 2;
}

/* Logo区域 */
.logo-section {
  text-align: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
}

.logo-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.logo-icon {
  width: 80px;
  height: 80px;
  position: relative;
}

.logo-svg {
  width: 100%;
  height: 100%;
  filter: drop-shadow(0 4px 12px rgba(126, 211, 33, 0.2));
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-4px);
  }
}

.logo-text {
  text-align: center;
}

.brand-name {
  font-size: 24px;
  font-weight: 700;
  color: #1a1a1a;
  margin: 0 0 4px 0;
  letter-spacing: -0.5px;
}

.brand-tagline {
  font-size: 12px;
  color: #7ed321;
  margin: 0;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 1px;
}

/* 标题区域 */
.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-title {
  font-size: 24px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 8px 0;
  line-height: 1.3;
}

.login-subtitle {
  font-size: 14px;
  color: #666666;
  margin: 0;
  line-height: 1.4;
}

/* 表单区域 */
.login-form {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  margin-bottom: 8px;
}

/* 自定义输入框样式 */
.custom-field {
  --van-field-input-text-color: #1a1a1a;
  --van-field-placeholder-text-color: #999999;
  --van-field-border-color: #e5e5e5;
  --van-field-focus-border-color: #7ed321;
}

:deep(.van-field__control) {
  background-color: #f8f9fa;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  transition: all 0.3s ease;
}

:deep(.van-field__control:focus) {
  border-color: #7ed321;
  background-color: #ffffff;
  box-shadow: 0 0 0 3px rgba(126, 211, 33, 0.1);
}

/* 登录按钮 */
.login-button {
  --van-button-primary-background: #7ed321;
  --van-button-primary-border-color: #7ed321;
  --van-button-border-radius: 8px;
  --van-button-large-height: 48px;
  --van-button-large-font-size: 16px;
  font-weight: 500;
  margin-top: 8px;
  transition: all 0.3s ease;
}

:deep(.van-button--primary:active) {
  background-color: #6bc91a;
  border-color: #6bc91a;
}

/* 底部区域 */
.login-footer {
  text-align: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.footer-text {
  font-size: 14px;
  color: #666666;
  margin: 0;
}

.link-text {
  color: #7ed321;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.3s ease;
}

.link-text:hover {
  color: #6bc91a;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .login-container {
    padding: 16px;
  }

  .login-card {
    padding: 32px 20px 24px;
  }

  .logo-icon {
    width: 70px;
    height: 70px;
  }

  .brand-name {
    font-size: 20px;
  }

  .brand-tagline {
    font-size: 11px;
  }

  .logo-section {
    margin-bottom: 24px;
    padding-bottom: 20px;
  }

  .login-title {
    font-size: 20px;
  }

  .circle-1 {
    width: 150px;
    height: 150px;
    top: -75px;
    right: -75px;
  }

  .circle-2 {
    width: 120px;
    height: 120px;
    bottom: -60px;
    left: -60px;
  }
}
</style>
