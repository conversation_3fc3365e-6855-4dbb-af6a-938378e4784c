export declare const TabTitle: import("vue").DefineComponent<import("vue").ExtractPropTypes<{
    id: StringConstructor;
    dot: BooleanConstructor;
    type: StringConstructor;
    color: StringConstructor;
    title: StringConstructor;
    badge: (NumberConstructor | StringConstructor)[];
    shrink: BooleanConstructor;
    isActive: BooleanConstructor;
    disabled: BooleanConstructor;
    controls: StringConstructor;
    scrollable: BooleanConstructor;
    activeColor: StringConstructor;
    inactiveColor: StringConstructor;
    showZeroBadge: {
        type: BooleanConstructor;
        default: true;
    };
}>, () => import("vue/jsx-runtime").JSX.Element, {}, {}, {}, import("vue").ComponentOptionsMixin, import("vue").ComponentOptionsMixin, {}, string, import("vue").PublicProps, Readonly<import("vue").ExtractPropTypes<{
    id: StringConstructor;
    dot: BooleanConstructor;
    type: StringConstructor;
    color: StringConstructor;
    title: StringConstructor;
    badge: (NumberConstructor | StringConstructor)[];
    shrink: BooleanConstructor;
    isActive: BooleanConstructor;
    disabled: BooleanConstructor;
    controls: StringConstructor;
    scrollable: BooleanConstructor;
    activeColor: StringConstructor;
    inactiveColor: StringConstructor;
    showZeroBadge: {
        type: BooleanConstructor;
        default: true;
    };
}>> & Readonly<{}>, {
    dot: boolean;
    disabled: boolean;
    shrink: boolean;
    scrollable: boolean;
    isActive: boolean;
    showZeroBadge: boolean;
}, {}, {}, {}, string, import("vue").ComponentProvideOptions, true, {}, any>;
